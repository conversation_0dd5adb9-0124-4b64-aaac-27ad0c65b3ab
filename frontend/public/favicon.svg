<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="atGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f0f0;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="32" cy="32" r="30" fill="url(#bgGradient)" stroke="#4a5568" stroke-width="2"/>
  
  <!-- @ 符号 -->
  <g transform="translate(32, 32)">
    <!-- 外圈 -->
    <circle cx="0" cy="0" r="18" fill="none" stroke="url(#atGradient)" stroke-width="3.5"/>
    
    <!-- 内圈 -->
    <circle cx="0" cy="0" r="8" fill="none" stroke="url(#atGradient)" stroke-width="2.5"/>
    
    <!-- 内圈填充 -->
    <circle cx="0" cy="0" r="4" fill="url(#atGradient)"/>
    
    <!-- 垂直线 -->
    <line x1="8" y1="-8" x2="8" y2="12" stroke="url(#atGradient)" stroke-width="3" stroke-linecap="round"/>
    
    <!-- 底部弯钩 -->
    <path d="M 8 12 Q 12 16 16 12 Q 18 10 16 8" fill="none" stroke="url(#atGradient)" stroke-width="3" stroke-linecap="round"/>
  </g>
  
  <!-- 装饰性光点 -->
  <circle cx="20" cy="20" r="1.5" fill="#ffffff" opacity="0.8"/>
  <circle cx="48" cy="16" r="1" fill="#ffffff" opacity="0.6"/>
  <circle cx="50" cy="44" r="1.2" fill="#ffffff" opacity="0.7"/>
</svg>
